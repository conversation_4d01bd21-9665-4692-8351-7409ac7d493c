/**
 * Google Apps Script for extracting Ukrainian license plate numbers
 * Processes data in column A and extracts vehicle and trailer license plates
 * Writes vehicle plates to column B and trailer plates to column C
 */

/**
 * Main function to process license plates in the active sheet
 */
function processLicensePlates() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();

  // Skip header row, start from row 2
  for (let i = 1; i < values.length; i++) {
    const text = values[i][0]; // Column A

    if (text && typeof text === 'string' && text.trim() !== '') {
      const extractedPlates = extractLicensePlates(text);

      // Write vehicle plate to column B (index 1)
      if (extractedPlates.vehicle) {
        sheet.getRange(i + 1, 2).setValue(extractedPlates.vehicle);
      }

      // Write trailer plate to column C (index 2)
      if (extractedPlates.trailer) {
        sheet.getRange(i + 1, 3).setValue(extractedPlates.trailer);
      }
    }
  }

  SpreadsheetApp.getUi().alert('License plate extraction completed!');
}

/**
 * Extract license plates from text
 * @param {string} text - Input text containing license plate information
 * @returns {Object} Object with vehicle and trailer license plates
 */
function extractLicensePlates(text) {
  // Normalize the text first
  const normalizedText = normalizeText(text);

  // Find all potential license plates using enhanced pattern matching
  const allPlates = findAllLicensePlatesEnhanced(normalizedText);

  // Separate vehicle and trailer plates
  return categorizePlates(allPlates);
}

/**
 * Normalize text by handling spaces, case, and mixed Cyrillic/Latin characters
 * @param {string} text - Input text
 * @returns {string} Normalized text
 */
function normalizeText(text) {
  if (!text) return '';

  // Convert to uppercase
  let normalized = text.toUpperCase();

  // Replace common Latin letters with Cyrillic equivalents
  const latinToCyrillic = {
    'A': 'А', 'B': 'В', 'E': 'Е', 'I': 'І', 'K': 'К',
    'M': 'М', 'H': 'Н', 'O': 'О', 'P': 'Р', 'C': 'С',
    'T': 'Т', 'X': 'Х'
  };

  // Replace Latin with Cyrillic
  for (const [latin, cyrillic] of Object.entries(latinToCyrillic)) {
    normalized = normalized.replace(new RegExp(latin, 'g'), cyrillic);
  }

  return normalized;
}

/**
 * Find all license plates in normalized text
 * @param {string} text - Normalized text
 * @returns {Array} Array of found license plates
 */
function findAllLicensePlates(text) {
  // Ukrainian license plate pattern: 2 letters + 4 digits + 2 letters
  // Valid Cyrillic letters: А, В, Е, І, К, М, Н, О, Р, С, Т, Х
  const validLetters = '[АВЕІКМНОРСТХ]';

  // Pattern for Ukrainian license plates with optional spaces
  const platePattern = new RegExp(
    validLetters + '{2}\\s*\\d{4}\\s*' + validLetters + '{2}',
    'g'
  );

  const plates = [];
  let match;

  while ((match = platePattern.exec(text)) !== null) {
    // Remove spaces from the matched plate
    const cleanPlate = match[0].replace(/\s/g, '');

    // Validate the plate format more strictly
    if (isValidUkrainianPlate(cleanPlate)) {
      plates.push(cleanPlate);
    }
  }

  return plates;
}

/**
 * Validate if a string is a valid Ukrainian license plate
 * @param {string} plate - License plate string
 * @returns {boolean} True if valid Ukrainian license plate
 */
function isValidUkrainianPlate(plate) {
  if (!plate || plate.length !== 8) return false;

  // Valid Cyrillic letters for Ukrainian plates
  const validLetters = 'АВЕІКМНОРСТХ';

  // Check format: 2 letters + 4 digits + 2 letters
  for (let i = 0; i < 2; i++) {
    if (!validLetters.includes(plate[i])) return false;
  }

  for (let i = 2; i < 6; i++) {
    if (!/\d/.test(plate[i])) return false;
  }

  for (let i = 6; i < 8; i++) {
    if (!validLetters.includes(plate[i])) return false;
  }

  return true;
}

/**
 * Categorize plates into vehicle and trailer plates
 * @param {Array} plates - Array of license plates
 * @returns {Object} Object with vehicle and trailer plates
 */
function categorizePlates(plates) {
  if (!plates || plates.length === 0) {
    return { vehicle: null, trailer: null };
  }

  // Remove duplicates
  const uniquePlates = [...new Set(plates)];

  // Separate trailer plates (those ending with Х + letter)
  const trailerPlates = [];
  const vehiclePlates = [];

  uniquePlates.forEach(plate => {
    // Check if it's a trailer plate (suffix starts with Х)
    if (plate.length === 8 && plate[6] === 'Х') {
      trailerPlates.push(plate);
    } else {
      vehiclePlates.push(plate);
    }
  });

  // Determine vehicle and trailer plates based on Ukrainian logic
  let vehicle = null;
  let trailer = null;

  if (vehiclePlates.length > 0) {
    vehicle = vehiclePlates[0]; // First vehicle plate
  }

  if (trailerPlates.length > 0) {
    trailer = trailerPlates[0]; // First trailer plate
  } else if (uniquePlates.length > 1) {
    // If no explicit trailer plates but multiple plates, second one might be trailer
    trailer = uniquePlates[1];
  }

  return { vehicle, trailer };
}

/**
 * Test function to validate the license plate extraction
 */
function testLicensePlateExtraction() {
  const testCases = [
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",
    "Перевезення вантажів по маршруту: смт. Малинівка, Чугуївський район , Харківська область - с. Степанки, Черкаський район Черкаська область, водій Ченбай Володимир Олександрович, автомобіль DAF CA3927ІТ спеціалізований вантажний сідловий тягач -Е, Причіп: SCHWARZMULLER CА1451ХF напівпричіп - загальний ,н/пр-тентований-Е. ТТН №Р922 від 20.10.2023",
    "Транспортні послуги с. Зоря (Рівненська обл.) - с.Степанки (Черкаська обл.), ТТН №9424446 від 30.06.2023р., авт. ДАФ № СА5761АН,н/пр № СА5462XF"
  ];

  console.log('Testing license plate extraction:');

  testCases.forEach((testCase, index) => {
    console.log(`\nTest case ${index + 1}:`);
    console.log(`Input: ${testCase}`);

    const result = extractLicensePlates(testCase);
    console.log(`Vehicle: ${result.vehicle}`);
    console.log(`Trailer: ${result.trailer}`);
  });
}

/**
 * Enhanced function to find license plates with better pattern matching
 * Handles various formats and edge cases
 * @param {string} text - Normalized text
 * @returns {Array} Array of found license plates
 */
function findAllLicensePlatesEnhanced(text) {
  const plates = [];

  // Primary pattern: Standard Ukrainian format
  const standardPlates = findAllLicensePlates(text);
  plates.push(...standardPlates);

  // Secondary pattern: Handle plates with mixed separators
  const mixedPattern = /([АВЕІКМНОРСТХ]{2})\s*[-\s]*(\d{4})\s*[-\s]*([АВЕІКМНОРСТХ]{2})/g;
  let match;

  while ((match = mixedPattern.exec(text)) !== null) {
    const plate = match[1] + match[2] + match[3];
    if (isValidUkrainianPlate(plate) && !plates.includes(plate)) {
      plates.push(plate);
    }
  }

  // Tertiary pattern: Handle plates with dots or other separators
  const separatorPattern = /([АВЕІКМНОРСТХ]{2})[.\s]*(\d{4})[.\s]*([АВЕІКМНОРСТХ]{2})/g;

  while ((match = separatorPattern.exec(text)) !== null) {
    const plate = match[1] + match[2] + match[3];
    if (isValidUkrainianPlate(plate) && !plates.includes(plate)) {
      plates.push(plate);
    }
  }

  return plates;
}

/**
 * Process a single row of data
 * @param {string} text - Text from column A
 * @returns {Object} Object with vehicle and trailer plates
 */
function processSingleRow(text) {
  if (!text || typeof text !== 'string') {
    return { vehicle: null, trailer: null };
  }

  return extractLicensePlates(text);
}

/**
 * Process selected range instead of entire sheet
 */
function processSelectedRange() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const range = sheet.getActiveRange();

  if (!range) {
    SpreadsheetApp.getUi().alert('Please select a range first!');
    return;
  }

  const values = range.getValues();
  const startRow = range.getRow();

  for (let i = 0; i < values.length; i++) {
    const text = values[i][0]; // Assuming column A is the first column in selection

    if (text && typeof text === 'string' && text.trim() !== '') {
      const extractedPlates = extractLicensePlates(text);

      // Write to columns B and C relative to the selection
      if (extractedPlates.vehicle) {
        sheet.getRange(startRow + i, 2).setValue(extractedPlates.vehicle);
      }

      if (extractedPlates.trailer) {
        sheet.getRange(startRow + i, 3).setValue(extractedPlates.trailer);
      }
    }
  }

  SpreadsheetApp.getUi().alert('Selected range processed!');
}

/**
 * Clear results in columns B and C
 */
function clearResults() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const lastRow = sheet.getLastRow();

  if (lastRow > 1) {
    // Clear columns B and C from row 2 onwards
    sheet.getRange(2, 2, lastRow - 1, 2).clearContent();
    SpreadsheetApp.getUi().alert('Results cleared!');
  }
}

/**
 * Create sample data for testing
 */
function createSampleData() {
  const sheet = SpreadsheetApp.getActiveSheet();

  // Clear existing data
  sheet.clear();

  // Add headers
  sheet.getRange(1, 1).setValue('Номенклатура товарів/послуг');
  sheet.getRange(1, 2).setValue('Vehicle License Plate');
  sheet.getRange(1, 3).setValue('Trailer License Plate');

  // Add sample data
  const sampleData = [
    "Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М. ТТН 0555 від 07.01.24р.",
    "Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО",
    "Перевезення вантажів по маршруту: смт. Малинівка, Чугуївський район , Харківська область - с. Степанки, Черкаський район Черкаська область, водій Ченбай Володимир Олександрович, автомобіль DAF CA3927ІТ спеціалізований вантажний сідловий тягач -Е, Причіп: SCHWARZMULLER CА1451ХF напівпричіп - загальний ,н/пр-тентований-Е. ТТН №Р922 від 20.10.2023",
    "Транспортні послуги с. Зоря (Рівненська обл.) - с.Степанки (Черкаська обл.), ТТН №9424446 від 30.06.2023р., авт. ДАФ № СА5761АН,н/пр № СА5462XF"
  ];

  // Add sample data to column A
  for (let i = 0; i < sampleData.length; i++) {
    sheet.getRange(i + 2, 1).setValue(sampleData[i]);
  }

  // Format headers
  const headerRange = sheet.getRange(1, 1, 1, 3);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#E8F0FE');

  // Auto-resize columns
  sheet.autoResizeColumns(1, 3);

  SpreadsheetApp.getUi().alert('Sample data created! You can now test the license plate extraction.');
}

/**
 * Create menu in Google Sheets for easy access
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('License Plate Extractor')
    .addItem('Process All License Plates', 'processLicensePlates')
    .addItem('Process Selected Range', 'processSelectedRange')
    .addSeparator()
    .addItem('Test Extraction', 'testLicensePlateExtraction')
    .addItem('Create Sample Data', 'createSampleData')
    .addItem('Clear Results', 'clearResults')
    .addToUi();
}